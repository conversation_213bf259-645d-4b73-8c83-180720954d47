#!/usr/bin/env python3
"""
Скрипт для тестирования исправления обработки таймаутов в опросах
"""
import asyncio
import logging
from datetime import datetime

# Настройка логирования для тестирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s | %(levelname)-8s | %(message)s",
    datefmt="%H:%M:%S"
)

# Имитация глобальных переменных из homework_quiz.py
active_questions = {}
completed_questions = set()

async def simulate_question_timeout(question_uuid: str, timeout_seconds: int):
    """Имитация функции handle_question_timeout_reliable"""
    try:
        logging.info(f"⏰ Запущен таймер для вопроса UUID: {question_uuid} на {timeout_seconds} секунд")
        await asyncio.sleep(timeout_seconds)

        # Проверяем, что вопрос еще активен
        if question_uuid not in active_questions:
            logging.info(f"🔄 Вопрос {question_uuid} уже не активен, таймаут отменен")
            return

        question_info = active_questions[question_uuid]
        
        # Проверяем, был ли уже дан ответ
        if question_info["answered"]:
            logging.info(f"✅ На вопрос {question_uuid} уже ответили, таймаут отменен")
            del active_questions[question_uuid]
            return

        logging.info(f"⏰ ТАЙМАУТ! Обрабатываем истечение времени для вопроса {question_uuid}")
        
        # Имитируем обработку таймаута
        completed_questions.add(question_uuid)
        del active_questions[question_uuid]
        
        logging.info(f"✅ Таймаут обработан для {question_uuid}")

    except Exception as e:
        logging.error(f"❌ Ошибка в обработчике таймаута для {question_uuid}: {e}")
        if question_uuid in active_questions:
            del active_questions[question_uuid]

async def simulate_answer(question_uuid: str, delay: float):
    """Имитация ответа на вопрос"""
    await asyncio.sleep(delay)
    
    if question_uuid in active_questions:
        active_questions[question_uuid]["answered"] = True
        logging.info(f"✅ Получен ответ на вопрос {question_uuid}")
    else:
        logging.info(f"❌ Вопрос {question_uuid} уже не активен при попытке ответить")

async def test_scenario_1():
    """Тест 1: Нормальный ответ до истечения времени"""
    logging.info("\n" + "="*50)
    logging.info("ТЕСТ 1: Ответ до истечения времени")
    logging.info("="*50)
    
    question_uuid = "test-1-uuid"
    active_questions[question_uuid] = {
        "chat_id": 12345,
        "answered": False,
        "start_time": datetime.now()
    }
    
    # Запускаем таймер на 3 секунды
    timeout_task = asyncio.create_task(simulate_question_timeout(question_uuid, 3))
    
    # Отвечаем через 1 секунду
    answer_task = asyncio.create_task(simulate_answer(question_uuid, 1))
    
    await asyncio.gather(timeout_task, answer_task)
    
    # Проверяем результат
    assert question_uuid not in active_questions, "Вопрос должен быть удален из активных"
    assert question_uuid not in completed_questions, "Вопрос не должен быть в завершенных (ответили вовремя)"
    
    logging.info("✅ ТЕСТ 1 ПРОЙДЕН")

async def test_scenario_2():
    """Тест 2: Таймаут без ответа"""
    logging.info("\n" + "="*50)
    logging.info("ТЕСТ 2: Таймаут без ответа")
    logging.info("="*50)
    
    question_uuid = "test-2-uuid"
    active_questions[question_uuid] = {
        "chat_id": 12345,
        "answered": False,
        "start_time": datetime.now()
    }
    
    # Запускаем таймер на 2 секунды, не отвечаем
    timeout_task = asyncio.create_task(simulate_question_timeout(question_uuid, 2))
    
    await timeout_task
    
    # Проверяем результат
    assert question_uuid not in active_questions, "Вопрос должен быть удален из активных"
    assert question_uuid in completed_questions, "Вопрос должен быть в завершенных (таймаут)"
    
    logging.info("✅ ТЕСТ 2 ПРОЙДЕН")

async def test_scenario_3():
    """Тест 3: Множественные вопросы одновременно"""
    logging.info("\n" + "="*50)
    logging.info("ТЕСТ 3: Множественные вопросы одновременно")
    logging.info("="*50)
    
    # Создаем 3 вопроса
    questions = []
    tasks = []
    
    for i in range(3):
        question_uuid = f"test-3-uuid-{i}"
        active_questions[question_uuid] = {
            "chat_id": 12345 + i,
            "answered": False,
            "start_time": datetime.now()
        }
        questions.append(question_uuid)
        
        # Разные таймеры: 1, 2, 3 секунды
        timeout_task = asyncio.create_task(simulate_question_timeout(question_uuid, i + 1))
        tasks.append(timeout_task)
        
        # Отвечаем только на второй вопрос (через 1.5 секунды)
        if i == 1:
            answer_task = asyncio.create_task(simulate_answer(question_uuid, 1.5))
            tasks.append(answer_task)
    
    await asyncio.gather(*tasks)
    
    # Проверяем результаты
    assert questions[0] in completed_questions, "Первый вопрос должен быть завершен по таймауту"
    assert questions[1] not in completed_questions, "Второй вопрос не должен быть завершен (ответили)"
    assert questions[2] in completed_questions, "Третий вопрос должен быть завершен по таймауту"
    
    for q in questions:
        assert q not in active_questions, f"Вопрос {q} должен быть удален из активных"
    
    logging.info("✅ ТЕСТ 3 ПРОЙДЕН")

async def main():
    """Главная функция тестирования"""
    logging.info("🚀 Запуск тестирования обработки таймаутов")
    
    try:
        await test_scenario_1()
        await test_scenario_2()
        await test_scenario_3()
        
        logging.info("\n" + "="*50)
        logging.info("🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ УСПЕШНО!")
        logging.info(f"📊 Активных вопросов: {len(active_questions)}")
        logging.info(f"📊 Завершенных вопросов: {len(completed_questions)}")
        logging.info("="*50)
        
    except Exception as e:
        logging.error(f"❌ Ошибка в тестах: {e}")
        import traceback
        logging.error(f"📋 Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    asyncio.run(main())
