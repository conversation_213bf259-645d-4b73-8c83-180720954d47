from student.handlers.homework_quiz import QuizStates, confirm_test
from student.handlers.homework import choose_homework, HomeworkStates

# Словарь переходов между состояниями
STATE_TRANSITIONS = {
    QuizStates.testing: QuizStates.confirming,
    QuizStates.confirming: HomeworkStates.homework,  # Возврат к выбору домашних заданий
}

# Словарь обработчиков для каждого состояния
STATE_HANDLERS = {
    QuizStates.confirming: confirm_test,  # Обработчик подтверждения квиза
    HomeworkStates.homework: choose_homework,  # При возврате из квиза идем к выбору ДЗ
}
